<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'
import RadioButton from "primevue/radiobutton"
import DatePicker from "primevue/datepicker"
import Address from '../../../Components/Form/Address.vue'
import RealEstateRegistry from '../../../Components/Form/RealEstateRegistry.vue'
import PersonalData from '../../../Components/Form/PersonalData.vue'
import AxaHealthQuestionnaire from "@/Components/Form/AxaHealthQuestionnaire.vue";
import Checkbox from "primevue/checkbox";

export default {
    components: {
        Checkbox,
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
        RadioButton,
        DatePicker,
        Address,
        RealEstateRegistry,
        PersonalData,
        AxaHealthQuestionnaire
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        const form = useForm({

            statoResidenza: '',
            peso: 0,
            altezza: 0,
            iban: '',
            banca: '',
            PEP: null,
            incaricoPubblico: null,
            professione: '',
            sport: '',
            estero: null,

            // Dati contraente
            datiContraente: {
                nome: '',
                cognome: '',
                via: '',
                numVia: '',
                cap: '',
                localita: '',
                provincia: '',
                telefono: '',
                email: ''
            },
            sesso: '',
            CF: '',
            PEPContraente: null,
            incaricoPubblicoContraente: null,

        });

        // DEBUG: valorizza con dati di esempio
        if (import.meta.env.DEV) {
            Object.assign(form, {

                statoResidenza: 'Italia',
                peso: 80,
                altezza: 180,
                iban: '****************************',
                banca: 'Banca di Debug',
                PEP: '0',
                incaricoPubblico: '0',
                professione: 'Ingegnere',
                sport: 'Calcio',
                estero: '0',

                datiContraente: {
                    nome: 'Mario',
                    cognome: 'Debug',
                    via: 'Via Test',
                    numVia: '123',
                    cap: '00143',
                    localita: 'Roma',
                    provincia: 'RM',
                    telefono: '38090332782',
                    email: '<EMAIL>'
                },
                sesso: 'Maschio',
                CF: 'PRZRCR66H501R',
                PEPContraente: '0',
                incaricoPubblicoContraente: '1',

            });
        }

        return {
            form,
            loading: false,
        }
    },
    methods: {
        submit() {
            this.loading = true;

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        },


    },
    watch: {
        // Se viene selezionato un pacchetto 3 o 4, la durata deve essere 5 anni.
        'form.pacchetto': function(newVal, oldVal) {
            if (newVal > 2) {
                if (this.form.durata === '10') {
                    this.form.durata = '5';
                }
            }
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p v-if="issuance.product.processType === 'direct'" class="text-gray-500 text-sm font-normal">Modulo di adesione</p>
                                <p v-else class="text-gray-500 text-sm font-normal">Modulo di raccolta dati</p>
                            </span>
                        </h2>

                        <hr class="my-4">

                        <h2 class="mb-2 mt-14">Dati aggiuntivi assicurato</h2>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.statoResidenza" placeholder="Stato di residenza" />
                                <FormMessage :errors="form.errors" field="statoResidenza">Inserisci lo stato di residenza</FormMessage>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <InputNumber locale="it-IT" class="w-full" v-model="form.peso" placeholder="Peso" />
                                <FormMessage :errors="form.errors" field="peso">Inserisci il peso in Kg</FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputNumber class="w-full" v-model="form.altezza" placeholder="Altezza" />
                                <FormMessage :errors="form.errors" field="altezza">Inserisci l'altezza in cm</FormMessage>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.iban" placeholder="IBAN" />
                                <FormMessage :errors="form.errors" field="iban">Inserisci l'IBAN</FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.banca" placeholder="Banca" />
                                <FormMessage :errors="form.errors" field="banca">Inserisci la banca</FormMessage>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="PEP">PEP</label>
                                <div class="flex items-center gap-5 mb-2">
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.PEP" inputId="PEP-1" name="PEP" value="1" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="PEP-1">Si</label>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.PEP" inputId="PEP-0" name="PEP" value="0" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="PEP-0">No</label>
                                    </div>
                                </div>
                                <FormMessage :errors="form.errors" field="PEP"></FormMessage>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="incaricoPubblico">Incarico pubblico</label>
                                <div class="flex items-center gap-5 mb-2">
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.incaricoPubblico" inputId="incaricoPubblico-1" name="incaricoPubblico" value="1" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="incaricoPubblico-1">Si</label>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.incaricoPubblico" inputId="incaricoPubblico-0" name="incaricoPubblico" value="0" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="incaricoPubblico-0">No</label>
                                    </div>
                                </div>
                                <FormMessage :errors="form.errors" field="incaricoPubblico"></FormMessage>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.professione" placeholder="Professione" />
                                <FormMessage :errors="form.errors" field="professione">Inserisci la professione</FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.sport" placeholder="Sport" />
                                <FormMessage :errors="form.errors" field="sport">Inserisci gli sport praticati</FormMessage>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium leading-6 text-gray-900" for="estero">Risiede, soggiorna o è indotto a viaggiare, a titolo professionale o con obiettivi umanitari, in paesi diversi da quelli
                                dell'Unione Europea e di Svizzera, Stati Uniti, Giappone, Canada, Norvegia, Islanda, Australia e Nuova Zelanda?</label>
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.estero" inputId="estero-1" name="estero" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="estero-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.estero" inputId="estero-0" name="estero" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="estero-0">No</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="estero"></FormMessage>
                        </div>

                        <h2 class="mb-2 mt-14">Dati contraente (se non coincide con l'assicurato)</h2>
                        <PersonalData
                            :personalData="form.datiContraente"
                            :errors="{messages: form.errors, key: 'datiContraente'}"
                        />
                        <div class="grid grid-cols-2 gap-x-5 mt-6 mb-3">
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.CF" placeholder="Codice fiscale" />
                                <FormMessage :errors="form.errors" field="CF">Inserisci il codice fiscale</FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputText class="w-full" v-model="form.sesso" placeholder="Sesso" />
                                <FormMessage :errors="form.errors" field="sesso">Inserisci il sesso</FormMessage>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-3">
                            <div class="col-span-1">
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="PEPContraente">PEP</label>
                                <div class="flex items-center gap-5 mb-2">
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.PEPContraente" inputId="PEPContraente-1" name="PEPContraente" value="1" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="PEPContraente-1">Si</label>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.PEPContraente" inputId="PEPContraente-0" name="PEPContraente" value="0" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="PEPContraente-0">No</label>
                                    </div>
                                </div>
                                <FormMessage :errors="form.errors" field="PEPContraente"></FormMessage>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium leading-6 text-gray-900" for="incaricoPubblicoContraente">Incarico pubblico</label>
                                <div class="flex items-center gap-5 mb-2">
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.incaricoPubblicoContraente" inputId="incaricoPubblicoContraente-1" name="incaricoPubblicoContraente" value="1" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="incaricoPubblicoContraente-1">Si</label>
                                    </div>
                                    <div class="flex items-center gap-1">
                                        <RadioButton v-model="form.incaricoPubblicoContraente" inputId="incaricoPubblicoContraente-0" name="incaricoPubblicoContraente" value="0" />
                                        <label class="block text-sm font-medium leading-6 text-gray-900" for="incaricoPubblicoContraente-0">No</label>
                                    </div>
                                </div>
                                <FormMessage :errors="form.errors" field="incaricoPubblicoContraente"></FormMessage>
                            </div>
                        </div>

                        <h2 class="mb-2 mt-14">Dati beneficiario 1</h2>
                        

                        <div class="mt-6 flex justify-center">
                            <Button type="submit" icon="pi pi-save" label="Salva" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>

<style scoped>
table tr {
    background-color: transparent;
}
</style>
